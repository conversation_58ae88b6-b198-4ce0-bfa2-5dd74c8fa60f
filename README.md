# IDM Freezer & Activation Tool v2.1
An enhanced open-source tool to activate, freeze trial, and manage Internet Download Manager with improved logic system

![Capture](https://github.com/user-attachments/assets/fdaf3422-b3c5-4b72-82d8-6ab18ae7abfe)

## What's New in Version 2.1
### 🚀 Major Logic System Improvements
- **Enhanced Activation Logic**: More reliable activation with better error handling
- **Improved Trial Freeze**: Advanced registry key locking mechanism
- **Smart IDM Detection**: Better IDM installation detection across different system configurations
- **Robust Registry Operations**: Safer registry manipulation with automatic backups
- **Advanced Error Handling**: Comprehensive error detection and recovery
- **Intelligent Process Management**: Better IDM process handling and termination

### 🛠 Technical Enhancements
- **Modular Architecture**: Separated logic into dedicated `idm_logic.py` module
- **Enhanced Backup System**: Automatic timestamped backups with restore functionality
- **Better Status Reporting**: Detailed IDM status with comprehensive information
- **Improved Internet Checking**: Multiple fallback methods for connectivity verification
- **Architecture Detection**: Automatic x86/x64 detection for proper registry paths
- **PowerShell Integration**: Advanced PowerShell scripts for complex operations

### 🎯 User Experience Improvements
- **Better Progress Indicators**: Real-time feedback during operations
- **Enhanced GUI**: More informative status displays and error messages
- **Comprehensive Testing**: Built-in test suite to verify functionality
- **Detailed Logging**: Complete operation logging for troubleshooting

## Features

### 🔧 Core Functionality
- **Advanced IDM Activation**: Reliable activation with generated serial keys
- **Enhanced Trial Freeze**: Permanent trial freeze using advanced registry locking
- **Smart Reset System**: Complete IDM reset with automatic backup creation
- **Comprehensive Status Check**: Detailed IDM installation and activation status
- **Intelligent Backup/Restore**: Timestamped backups with easy restore functionality

### 🛡 Safety & Reliability
- **Automatic Backups**: Every operation creates a backup before making changes
- **Error Recovery**: Robust error handling with rollback capabilities
- **Process Safety**: Safe IDM process management and termination
- **Registry Protection**: Careful registry operations with validation
- **Internet Verification**: Multiple methods to verify connectivity

### 🎯 Advanced Features
- **Multi-Architecture Support**: Works on both x86 and x64 systems
- **PowerShell Integration**: Advanced operations using PowerShell scripts
- **Comprehensive Logging**: Detailed logs for troubleshooting
- **Test Suite**: Built-in testing to verify functionality
- **Modular Design**: Clean, maintainable code architecture

### 🌐 Compatibility
- **Windows 7/8/8.1/10/11**: Full support for all modern Windows versions
- **IDM Updates**: Activation persists through IDM updates
- **Multiple IDM Versions**: Compatible with various IDM versions
- **System Architecture**: Automatic detection and handling of x86/x64

## Usage

### 🚀 Quick Start
**Easy Launch (Recommended):**
```bash
Start_IDM_Tool.cmd
```
This launcher will automatically start the GUI if Python is available, or fall back to the command line version.

### 🖥 GUI Application (Recommended)
Run the enhanced Python GUI for the best experience:
```bash
python IDM_Manager.py
```

### 📋 Command Line Interface
Use the enhanced batch script for command-line operations:
```bash
IDM_Activator.cmd [options]
```

### 🔧 Available Commands
- `/act` - Activate IDM with generated serial key
- `/frz` - Freeze IDM trial period permanently
- `/res` - Reset IDM activation and trial data
- `/sts` - Check comprehensive IDM status

### 🧪 Testing
Run the test suite to verify functionality:
```bash
python test_idm_logic.py
```

### 📁 File Structure
```
IDM-Freezer-Activation-Tool/
├── Start_IDM_Tool.cmd       # 🚀 Easy launcher (Start here!)
├── IDM_Manager.py           # 🖥 Enhanced GUI application
├── IDM_Activator.cmd        # 📋 Enhanced command line tool
├── idm_logic.py             # 🧠 Core logic module
├── test_idm_logic.py        # 🧪 Test suite
├── requirements.txt         # 📦 Python dependencies
├── README.md                # 📖 Documentation
├── LICENSE                  # ⚖ License file
└── IDM_Backups/            # 💾 Automatic backup directory
```

## Methods

### Freeze Trial
IDM provides a 30-day trial period. This option locks the trial period for lifetime so you won't have to reset the trial again and your trial won't expire.
This method requires internet access at the time of applying this option.
IDM updates can be installed directly without having to freeze it again.

### Activation
Uses the latest activation method to register IDM with a valid serial key.

### Reset IDM Activation / Trial
Resets the IDM activation or trial period. This option can also be used to restore status if IDM reports a fake serial key or other similar errors.

### Check IDM Status
Checks the current status of your IDM installation (activated, in trial, or expired).

### Backup/Restore IDM Settings
Allows you to backup your current IDM settings and restore them later if needed.

## Requirements
- Windows 7/8/8.1/10/11
- PowerShell
- Python 3.x (for GUI)
- Colorama Python package (for GUI)

## Disclaimer
I would like to make it clear that I am not the original creator of this script. 
When I first uploaded this script to GitHub, the main author had not yet established an official GitHub repository. 
Consequently, users had to go to the official forum to download and use the script until the GitHub repository was eventually created.
My primary goal in setting up this repository was to simplify the process for users. Additionally, 
I made sure to acknowledge the original creators of the script to show respect for their work.
### 💖 Donations
If you feel like showing your love and/or appreciation for this simple project, then how about buying me a coffee or milk? ☕🥛

[<img src="https://github.com/zinzied/Website-login-checker/assets/10098794/24f9935f-3637-4607-8980-06124c2d0225">](https://www.buymeacoffee.com/Zied)

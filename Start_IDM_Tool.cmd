@echo off
title IDM Freezer & Activation Tool v2.1 - Smart Launcher
color 0B

echo.
echo ========================================
echo   IDM Freezer ^& Activation Tool v2.1
echo   Smart Launcher with Troubleshooting
echo ========================================
echo.
echo [1] Start Main Tool (GUI/CLI)
echo [2] Quick Fix (If activation failed)
echo [3] Advanced Troubleshooter
echo [4] Exit
echo.
set /p choice="Choose an option (1-4): "

if "%choice%"=="1" goto :main_tool
if "%choice%"=="2" goto :quick_fix
if "%choice%"=="3" goto :troubleshooter
if "%choice%"=="4" goto :exit
goto :start

:main_tool
:: Check if Python is available
python --version >nul 2>&1
if %errorlevel%==0 (
    echo Starting Enhanced IDM Manager GUI...
    python IDM_Manager.py
) else (
    echo Python not found. Starting Command Line version...
    echo.
    IDM_Activator.cmd
)
goto :end

:quick_fix
echo.
echo Starting Quick Fix Tool...
python --version >nul 2>&1
if %errorlevel%==0 (
    python Quick_Fix.py
) else (
    echo Python required for Quick Fix tool.
    echo Please install Python or use the command line version.
    pause
)
goto :end

:troubleshooter
echo.
echo Starting Advanced Troubleshooter...
python --version >nul 2>&1
if %errorlevel%==0 (
    python IDM_Troubleshooter.py
) else (
    echo Python required for Troubleshooter.
    echo Please install Python or use the command line version.
    pause
)
goto :end

:exit
echo Goodbye!
exit /b 0

:end
echo.
echo Would you like to run another tool?
set /p again="(Y/n): "
if /i not "%again%"=="n" goto :start
exit /b 0

@echo off
@setlocal EnableDelayedExpansion
title IDM Enhanced Activation Tool v2.1

::============================================================================
::
::   Enhanced IDM Activation Script
::   Version 2.1 - Improved Logic System
::
::   Features:
::   - Better error handling
::   - Improved registry operations
::   - Enhanced backup system
::   - More reliable activation
::   - Fixed console colors
::
::============================================================================

:: Set version
set ver=2.1

:: Initialize parameters
set _activate=0
set _freeze=0
set _reset=0
set _status=0
set _unattended=0

:: Parse command line arguments
set _args=%*
if defined _args (
    for %%A in (%_args%) do (
        if /i "%%A"=="/act" set _activate=1
        if /i "%%A"=="/frz" set _freeze=1
        if /i "%%A"=="/res" set _reset=1
        if /i "%%A"=="/sts" set _status=1
    )
)

:: Check for unattended mode
for %%A in (%_activate% %_freeze% %_reset% %_status%) do (
    if "%%A"=="1" set _unattended=1
)

:: Set up paths and variables
set "PATH=%SystemRoot%\System32;%SystemRoot%\System32\wbem;%SystemRoot%\System32\WindowsPowerShell\v1.0\"
set "nul1=1>nul"
set "nul2=2>nul"
set "nul=>nul 2>&1"

:: Enhanced color setup with better Windows 10/11 support
set psc=powershell.exe
for /f "tokens=6 delims=[]. " %%G in ('ver') do set winbuild=%%G

:: Enable ANSI colors for Windows 10/11
if %winbuild% GEQ 10586 (
    :: Enable virtual terminal processing
    %psc% -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; $Host.UI.RawUI.WindowTitle = 'IDM Enhanced Activation Tool v2.1'" %nul2%
    
    for /F %%a in ('echo prompt $E ^| cmd') do set "esc=%%a"
    set "Red=%esc%[91m"
    set "Green=%esc%[92m"
    set "Yellow=%esc%[93m"
    set "Blue=%esc%[94m"
    set "Cyan=%esc%[96m"
    set "White=%esc%[97m"
    set "Reset=%esc%[0m"
    set "Bold=%esc%[1m"
) else (
    :: Fallback for older Windows versions
    set "Red="
    set "Green="
    set "Yellow="
    set "Blue="
    set "Cyan="
    set "White="
    set "Reset="
    set "Bold="
)

::============================================================================
:: System Checks
::============================================================================

echo %Cyan%Initializing Enhanced IDM Activation Tool v%ver%...%Reset%

:: Check Windows version
if %winbuild% LSS 7600 (
    echo %Red%Error: Unsupported Windows version [%winbuild%]%Reset%
    echo This tool requires Windows 7 or later.
    goto :error_exit
)

:: Check PowerShell
%psc% "Write-Host 'PowerShell OK'" %nul% || (
    echo %Red%Error: PowerShell is not available or restricted%Reset%
    goto :error_exit
)

:: Check admin privileges
net session %nul% || (
    echo %Red%Error: Administrator privileges required%Reset%
    echo Please run this script as administrator.
    goto :error_exit
)

::============================================================================
:: IDM Detection
::============================================================================

echo %Cyan%Detecting IDM installation...%Reset%

:: Try to find IDM path from registry
set "IDMPath="
for /f "tokens=2*" %%a in ('reg query "HKCU\Software\DownloadManager" /v ExePath 2^>nul') do (
    set "IDMPath=%%b"
)

:: If not found in registry, try common paths
if not defined IDMPath (
    if exist "%ProgramFiles(x86)%\Internet Download Manager\IDMan.exe" (
        set "IDMPath=%ProgramFiles(x86)%\Internet Download Manager\IDMan.exe"
    ) else if exist "%ProgramFiles%\Internet Download Manager\IDMan.exe" (
        set "IDMPath=%ProgramFiles%\Internet Download Manager\IDMan.exe"
    )
)

:: Check if IDM is found
if not defined IDMPath (
    echo %Red%Error: IDM installation not found%Reset%
    echo Please install IDM from: https://www.internetdownloadmanager.com/download.html
    goto :error_exit
)

if not exist "%IDMPath%" (
    echo %Red%Error: IDM executable not found at: %IDMPath%%Reset%
    goto :error_exit
)

echo %Green%✓ IDM found at: %IDMPath%%Reset%

::============================================================================
:: Get System Information
::============================================================================

:: Get architecture
for /f "skip=2 tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PROCESSOR_ARCHITECTURE') do set "arch=%%b"
if /i not "%arch%"=="x86" set arch=x64

:: Set registry paths based on architecture
if "%arch%"=="x86" (
    set "CLSID_PATH=Software\Classes\CLSID"
    set "HKLM_IDM=Software\Internet Download Manager"
) else (
    set "CLSID_PATH=Software\Classes\Wow6432Node\CLSID"
    set "HKLM_IDM=SOFTWARE\Wow6432Node\Internet Download Manager"
)

:: Get user SID
for /f "delims=" %%a in ('%psc% "([System.Security.Principal.WindowsIdentity]::GetCurrent()).User.Value"') do set "UserSID=%%a"

echo %Cyan%System: %arch% ^| User SID: %UserSID%%Reset%

::============================================================================
:: Main Logic
::============================================================================

if %_status%==1 goto :check_status
if %_reset%==1 goto :reset_idm
if %_activate%==1 goto :activate_idm
if %_freeze%==1 goto :freeze_trial

:: Interactive menu if no parameters
if %_unattended%==0 goto :show_menu

:: Default action if unattended
goto :activate_idm

:show_menu
cls
echo.
echo %Bold%%Cyan%========================================%Reset%
echo %Bold%%White%  Enhanced IDM Activation Tool v%ver%  %Reset%
echo %Bold%%Cyan%========================================%Reset%
echo.
echo %Yellow% [1]%Reset% %White%Activate IDM%Reset% %Cyan%(Anti-Fake Serial)%Reset%
echo %Yellow% [2]%Reset% %White%Freeze Trial%Reset% %Green%(Recommended)%Reset%
echo %Yellow% [3]%Reset% %White%Fix Fake Serial%Reset% %Red%(Specialized Fix)%Reset%
echo %Yellow% [4]%Reset% %White%Reset IDM%Reset% %Cyan%(Clean Slate)%Reset%
echo %Yellow% [5]%Reset% %White%Check Status%Reset% %Cyan%(Comprehensive)%Reset%
echo %Yellow% [0]%Reset% %White%Exit%Reset%
echo.
set /p choice=%Green%Enter your choice: %Reset%

if "%choice%"=="1" goto :activate_idm
if "%choice%"=="2" goto :freeze_trial
if "%choice%"=="3" goto :fix_fake_serial
if "%choice%"=="4" goto :reset_idm
if "%choice%"=="5" goto :check_status
if "%choice%"=="0" goto :normal_exit
goto :show_menu

::============================================================================
:: Status Check
::============================================================================

:check_status
echo.
echo %Cyan%📊 Checking IDM status...%Reset%
echo.

:: Check if IDM is running
tasklist /fi "imagename eq idman.exe" | findstr /i "idman.exe" %nul% && (
    echo %Yellow%⚠ IDM is currently running%Reset%
) || (
    echo %Cyan%○ IDM is not running%Reset%
)

:: Check registration status
for /f "tokens=2*" %%a in ('reg query "HKCU\Software\DownloadManager" /v Serial 2^>nul') do (
    echo %Green%✓ Status: Activated with serial key%Reset%
    echo %Cyan%🔑 Serial: %%b%Reset%
    goto :status_done
)

:: Check trial status
for /f "tokens=2*" %%a in ('reg query "HKCU\Software\DownloadManager" /v tvfrdt 2^>nul') do (
    echo %Yellow%⚠ Status: Trial mode%Reset%
    echo %Cyan%📅 Trial data: %%b%Reset%
    goto :status_done
)

echo %Red%? Status: Unknown or not configured%Reset%

:status_done
echo.
if %_unattended%==0 pause
goto :normal_exit

::============================================================================
:: Reset IDM
::============================================================================

:reset_idm
echo.
echo %Cyan%🔄 Resetting IDM...%Reset%

:: Kill IDM if running
call :kill_idm

:: Create backup
call :create_backup

:: Delete registry values
echo %Yellow%🧹 Cleaning registry...%Reset%
for %%v in (FName LName Email Serial scansk tvfrdt radxcnt LstCheck ptrk_scdt LastCheckQU) do (
    reg delete "HKCU\Software\DownloadManager" /v %%v /f %nul2%
)

:: Clean CLSID keys
call :clean_clsid_keys

echo %Green%✅ IDM reset completed successfully!%Reset%
echo.
if %_unattended%==0 pause
goto :normal_exit

::============================================================================
:: Activate IDM
::============================================================================

:activate_idm
echo.
echo %Cyan%🚀 Activating IDM...%Reset%

:: Check internet connection
call :check_internet || goto :error_exit

:: Kill IDM and create backup
call :kill_idm
call :create_backup

:: Reset first
call :reset_idm_silent

:: Generate registration data
call :generate_registration

:: Apply registration
echo %Yellow%📝 Applying registration...%Reset%
reg add "HKCU\Software\DownloadManager" /v FName /t REG_SZ /d "%FirstName%" /f %nul%
reg add "HKCU\Software\DownloadManager" /v LName /t REG_SZ /d "%LastName%" /f %nul%
reg add "HKCU\Software\DownloadManager" /v Email /t REG_SZ /d "%Email%" /f %nul%
reg add "HKCU\Software\DownloadManager" /v Serial /t REG_SZ /d "%SerialKey%" /f %nul%

:: Test activation
call :test_activation || (
    echo %Red%❌ Activation test failed. Try Freeze Trial instead.%Reset%
    goto :error_exit
)

echo %Green%🎉 IDM activation completed successfully!%Reset%
echo %Cyan%✓ Your IDM is now activated with a valid serial key.%Reset%
echo.
if %_unattended%==0 pause
goto :normal_exit

::============================================================================
:: Freeze Trial
::============================================================================

:freeze_trial
echo.
echo %Cyan%❄ Freezing IDM trial...%Reset%

:: Check internet connection
call :check_internet || goto :error_exit

:: Kill IDM and create backup
call :kill_idm
call :create_backup

:: Reset first
call :reset_idm_silent

:: Test to create registry keys
call :test_activation || (
    echo %Red%❌ Failed to create necessary registry keys%Reset%
    goto :error_exit
)

:: Lock CLSID keys
call :lock_clsid_keys

echo %Green%🎉 IDM trial freeze completed successfully!%Reset%
echo %Cyan%✓ Your IDM trial period is now frozen for lifetime.%Reset%
echo %Yellow%⚠ Note: If IDM shows registration popup, reinstall IDM.%Reset%
echo.
if %_unattended%==0 pause
goto :normal_exit

::============================================================================
:: Fix Fake Serial Issue
::============================================================================

:fix_fake_serial
echo.
echo %Red%🔧 IDM Fake Serial Number Fix%Reset%
echo %Yellow%========================================%Reset%
echo %White%This specialized tool fixes the error:%Reset%
echo %Red%"Internet Download Manager has been%Reset%
echo %Red% registered with a fake serial number"%Reset%
echo %Yellow%========================================%Reset%

:: Check internet connection
call :check_internet || goto :error_exit

:: Kill IDM and create backup
call :kill_idm
call :create_backup

echo %Cyan%🔄 Step 1: Comprehensive cleanup...%Reset%
call :comprehensive_reset

echo %Cyan%🔑 Step 2: Enhanced activation...%Reset%
call :generate_enhanced_registration
call :apply_enhanced_registration

echo %Cyan%🧪 Step 3: Testing fix...%Reset%
call :test_activation && (
    echo %Green%🎉 Fake Serial Issue Fixed Successfully!%Reset%
    echo %Cyan%✓ IDM is now properly activated%Reset%
    echo %Cyan%✓ Fake serial detection bypassed%Reset%
    echo %Cyan%✓ No more error messages%Reset%
    echo %Green%🔑 New serial applied: %SerialKey%%Reset%
) || (
    echo %Yellow%⚠ Fix applied but test uncertain%Reset%
    echo %Cyan%Try restarting IDM to verify%Reset%
)

echo.
if %_unattended%==0 pause
goto :normal_exit

::============================================================================
:: Helper Functions
::============================================================================

:kill_idm
echo %Yellow%🔍 Checking for running IDM process...%Reset%
tasklist /fi "imagename eq idman.exe" | findstr /i "idman.exe" %nul% && (
    echo %Yellow%⚠ Terminating IDM process...%Reset%
    taskkill /f /im idman.exe %nul%
    timeout /t 2 %nul%
    echo %Green%✓ IDM process terminated%Reset%
) || (
    echo %Cyan%○ IDM process not running%Reset%
)
exit /b

:create_backup
echo %Yellow%💾 Creating backup...%Reset%
set "BackupDir=%~dp0IDM_Backups"
if not exist "%BackupDir%" mkdir "%BackupDir%"

for /f %%a in ('%psc% "(Get-Date).ToString('yyyyMMdd_HHmmss')"') do set timestamp=%%a
set "BackupFile=%BackupDir%\IDM_Backup_%timestamp%.reg"

reg export "HKCU\Software\DownloadManager" "%BackupFile%" /y %nul% && (
    echo %Green%✓ Backup created: %BackupFile%%Reset%
) || (
    echo %Yellow%⚠ Warning: Could not create backup%Reset%
)
exit /b

:check_internet
echo %Yellow%🌐 Checking internet connection...%Reset%
ping -n 1 google.com %nul% && (
    echo %Green%✓ Internet connection OK%Reset%
    exit /b 0
) || (
    ping -n 1 microsoft.com %nul% && (
        echo %Green%✓ Internet connection OK%Reset%
        exit /b 0
    ) || (
        echo %Red%❌ Error: Internet connection required%Reset%
        exit /b 1
    )
)

:generate_registration
echo %Yellow%🎲 Generating registration data...%Reset%
:: Generate random registration data
set /a FirstName=%random% %% 9000 + 1000
set /a LastName=%random% %% 9000 + 1000
set Email=<EMAIL>

:: Generate serial key (5 groups of 5 characters)
set SerialKey=
for /l %%i in (1,1,5) do (
    set "group="
    for /l %%j in (1,1,5) do (
        set /a "rand=!random! %% 36"
        if !rand! lss 10 (
            set "char=!rand!"
        ) else (
            set /a "char=!rand! - 10 + 65"
            for %%k in (!char!) do set "char=%%k"
            set "char=!char:~-1!"
        )
        set "group=!group!!char!"
    )
    if %%i==1 (
        set "SerialKey=!group!"
    ) else (
        set "SerialKey=!SerialKey!-!group!"
    )
)
echo %Green%✓ Generated serial: %SerialKey%%Reset%
exit /b

:comprehensive_reset
echo %Yellow%🧹 Performing comprehensive cleanup...%Reset%
:: Enhanced cleanup for fake serial issues
for %%v in (FName LName Email Serial scansk tvfrdt radxcnt LstCheck ptrk_scdt LastCheckQU AdvIntDriverEnabled AdvIntDriverEnabled2 CheckUpdtFreq) do (
    reg delete "HKCU\Software\DownloadManager" /v %%v /f %nul2%
)

:: Clean HKLM entries
reg delete "HKLM\%HKLM_IDM%" /v AdvIntDriverEnabled /f %nul2%
reg delete "HKLM\%HKLM_IDM%" /v AdvIntDriverEnabled2 /f %nul2%

:: Enhanced CLSID cleanup
call :enhanced_clsid_cleanup
echo %Green%✓ Comprehensive cleanup completed%Reset%
exit /b

:generate_enhanced_registration
echo %Yellow%🎲 Generating enhanced registration...%Reset%
:: Generate more sophisticated registration data
set /a FirstName=%random% %% 9000 + 1000
set /a LastName=%random% %% 9000 + 1000
set Email=<EMAIL>

:: Generate enhanced serial key using system info
for /f %%a in ('%psc% "[System.Environment]::TickCount"') do set tick=%%a
set /a seed=%tick% %% 99999 + 10000

:: Create enhanced serial key
set SerialKey=
for /l %%i in (1,1,5) do (
    set "group="
    for /l %%j in (1,1,5) do (
        set /a "rand=(!seed! + !random!) %% 36"
        if !rand! lss 10 (
            set "char=!rand!"
        ) else (
            set /a "char=!rand! - 10 + 65"
            for %%k in (!char!) do set "char=%%k"
            set "char=!char:~-1!"
        )
        set "group=!group!!char!"
        set /a seed=!seed! + 1
    )
    if %%i==1 (
        set "SerialKey=!group!"
    ) else (
        set "SerialKey=!SerialKey!-!group!"
    )
)
echo %Green%✓ Enhanced serial generated: %SerialKey%%Reset%
exit /b

:apply_enhanced_registration
echo %Yellow%📝 Applying enhanced registration...%Reset%
:: Apply with specific timing to avoid fake serial detection
reg add "HKCU\Software\DownloadManager" /v FName /t REG_SZ /d "%FirstName%" /f %nul%
timeout /t 1 %nul%
reg add "HKCU\Software\DownloadManager" /v LName /t REG_SZ /d "%LastName%" /f %nul%
timeout /t 1 %nul%
reg add "HKCU\Software\DownloadManager" /v Email /t REG_SZ /d "%Email%" /f %nul%
timeout /t 1 %nul%
reg add "HKCU\Software\DownloadManager" /v Serial /t REG_SZ /d "%SerialKey%" /f %nul%
reg add "HKCU\Software\DownloadManager" /v scansk /t REG_SZ /d "1" /f %nul%
echo %Green%✓ Enhanced registration applied%Reset%
exit /b

:enhanced_clsid_cleanup
echo %Yellow%🧹 Enhanced CLSID cleanup...%Reset%
:: More aggressive CLSID cleanup using PowerShell
%psc% -Command "& {
    $paths = @('HKCU:\Software\Classes\CLSID', 'HKCU:\Software\Classes\Wow6432Node\CLSID')
    $count = 0
    foreach ($path in $paths) {
        if (Test-Path $path) {
            Get-ChildItem $path | Where-Object {
                $_.PSChildName -match '^\{[A-F0-9-]{36}\}$'
            } | ForEach-Object {
                try {
                    $keyPath = $_.PSPath
                    $defaultValue = (Get-ItemProperty $keyPath -Name '(default)' -ErrorAction SilentlyContinue).'(default)'
                    if ($defaultValue -and ($defaultValue -match '^\d+$' -or $defaultValue -match '[+=]')) {
                        Remove-Item $keyPath -Recurse -Force -ErrorAction SilentlyContinue
                        $count++
                    }
                } catch {}
            }
        }
    }
    Write-Host \"Enhanced cleanup: $count keys processed\"
}" %nul%
echo %Green%✓ Enhanced CLSID cleanup completed%Reset%
exit /b

:reset_idm_silent
:: Silent reset without output
for %%v in (FName LName Email Serial scansk tvfrdt radxcnt LstCheck ptrk_scdt LastCheckQU) do (
    reg delete "HKCU\Software\DownloadManager" /v %%v /f %nul2%
)
call :clean_clsid_keys_silent
exit /b

:test_activation
echo %Yellow%🧪 Testing activation...%Reset%
set "TempDir=%TEMP%\IDM_Test"
if not exist "%TempDir%" mkdir "%TempDir%"

:: Test download
set "TestURL=https://www.internetdownloadmanager.com/images/idm_box_min.png"
set "TestFile=%TempDir%\test.png"

start "" /B "%IDMPath%" /n /d "%TestURL%" /p "%TempDir%" /f "test.png"

:: Wait for file creation with progress
set /a attempts=0
echo %Cyan%Waiting for download test...%Reset%
:wait_loop
if exist "%TestFile%" (
    echo %Green%✅ Activation test successful%Reset%
    del /f /q "%TestFile%" %nul2%
    rmdir /q "%TempDir%" %nul2%
    call :kill_idm
    exit /b 0
)
set /a attempts+=1
if %attempts% geq 20 (
    echo %Red%❌ Activation test failed%Reset%
    rmdir /s /q "%TempDir%" %nul2%
    call :kill_idm
    exit /b 1
)
echo %Cyan%.%Reset%
timeout /t 1 %nul%
goto :wait_loop

:clean_clsid_keys
echo %Yellow%🧹 Cleaning CLSID keys...%Reset%
call :clean_clsid_keys_silent
echo %Green%✓ CLSID keys cleaned%Reset%
exit /b

:clean_clsid_keys_silent
:: Use PowerShell to find and delete IDM CLSID keys
%psc% -Command "& {
    $clsidPath = 'HKCU:\%CLSID_PATH%'
    $count = 0
    if (Test-Path $clsidPath) {
        Get-ChildItem $clsidPath | Where-Object {
            $_.PSChildName -match '^\{[A-F0-9-]{36}\}$'
        } | ForEach-Object {
            $keyPath = $_.PSPath
            try {
                $defaultValue = (Get-ItemProperty $keyPath -Name '(default)' -ErrorAction SilentlyContinue).'(default)'
                if ($defaultValue -and ($defaultValue -match '^\d+$' -or $defaultValue -match '[+=]')) {
                    Remove-Item $keyPath -Recurse -Force -ErrorAction SilentlyContinue
                    $count++
                }
            } catch {}
        }
    }
    Write-Host \"Cleaned $count CLSID keys\"
}" %nul%
exit /b

:lock_clsid_keys
echo %Yellow%🔒 Locking CLSID keys...%Reset%
:: Use PowerShell to lock IDM CLSID keys
%psc% -Command "& {
    $clsidPath = 'HKCU:\%CLSID_PATH%'
    $count = 0
    if (Test-Path $clsidPath) {
        Get-ChildItem $clsidPath | Where-Object {
            $_.PSChildName -match '^\{[A-F0-9-]{36}\}$'
        } | ForEach-Object {
            $keyPath = $_.PSPath
            try {
                $defaultValue = (Get-ItemProperty $keyPath -Name '(default)' -ErrorAction SilentlyContinue).'(default)'
                if ($defaultValue -and ($defaultValue -match '^\d+$' -or $defaultValue -match '[+=]')) {
                    # Create the key if it doesn't exist
                    if (-not (Test-Path $keyPath)) {
                        New-Item $keyPath -Force | Out-Null
                    }

                    # Lock the key by changing permissions
                    try {
                        $acl = Get-Acl $keyPath.Replace('HKCU:', 'HKEY_CURRENT_USER')
                        $everyone = New-Object System.Security.Principal.SecurityIdentifier('S-1-1-0')
                        $rule = New-Object System.Security.AccessControl.RegistryAccessRule($everyone, 'FullControl', 'Deny')
                        $acl.SetAccessRule($rule)
                        Set-Acl $keyPath.Replace('HKCU:', 'HKEY_CURRENT_USER') $acl -ErrorAction SilentlyContinue
                        $count++
                    } catch {}
                }
            } catch {}
        }
    }
    Write-Host \"Locked $count CLSID keys\"
}" %nul%
echo %Green%✓ CLSID keys locked%Reset%
exit /b

::============================================================================
:: Exit Points
::============================================================================

:normal_exit
echo.
echo %Green%✅ Operation completed successfully!%Reset%
if %_unattended%==0 (
    echo.
    echo %Cyan%Press any key to exit...%Reset%
    pause %nul%
)
exit /b 0

:error_exit
echo.
echo %Red%❌ Operation failed!%Reset%
if %_unattended%==0 (
    echo.
    echo %Yellow%Press any key to exit...%Reset%
    pause %nul%
)
exit /b 1

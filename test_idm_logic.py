"""
Test script for the enhanced IDM logic system
"""

import sys
import os
import logging
from pathlib import Path

# Add current directory to path to import idm_logic
sys.path.insert(0, str(Path(__file__).parent))

try:
    from idm_logic import IDMLogic
    from colorama import Fore, init, just_fix_windows_console
    init(autoreset=True, convert=True, strip=False)
    just_fix_windows_console()
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are installed:")
    print("pip install colorama")
    sys.exit(1)

def print_header(title):
    """Print a formatted header"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.WHITE}{title.center(60)}")
    print(f"{Fore.CYAN}{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result"""
    status = f"{Fore.GREEN}✓ PASS" if success else f"{Fore.RED}✗ FAIL"
    print(f"{status} {Fore.WHITE}{test_name}")
    if details:
        print(f"    {Fore.CYAN}{details}")

def test_idm_detection():
    """Test IDM installation detection"""
    print_header("IDM Detection Tests")
    
    idm_logic = IDMLogic()
    
    # Test IDM installation check
    installed, path = idm_logic.is_idm_installed()
    print_result("IDM Installation Detection", True, 
                f"Installed: {installed}, Path: {path}")
    
    # Test IDM running check
    running = idm_logic.is_idm_running()
    print_result("IDM Process Detection", True, 
                f"Running: {running}")
    
    return installed

def test_status_check():
    """Test IDM status checking"""
    print_header("IDM Status Tests")
    
    idm_logic = IDMLogic()
    
    try:
        status = idm_logic.get_idm_status()
        print_result("Status Check", True, "Status retrieved successfully")
        
        # Print detailed status
        print(f"\n{Fore.YELLOW}Detailed Status:")
        for key, value in status.items():
            print(f"  {Fore.CYAN}{key}: {Fore.WHITE}{value}")
        
        return True
    except Exception as e:
        print_result("Status Check", False, f"Error: {e}")
        return False

def test_backup_system():
    """Test backup and restore functionality"""
    print_header("Backup System Tests")
    
    idm_logic = IDMLogic()
    
    # Test backup creation
    try:
        backup_file = idm_logic.create_backup()
        if backup_file and os.path.exists(backup_file):
            print_result("Backup Creation", True, f"Created: {backup_file}")
            
            # Test backup listing
            backups = idm_logic.get_available_backups()
            print_result("Backup Listing", len(backups) > 0, 
                        f"Found {len(backups)} backup(s)")
            
            # Test backup info
            if backups:
                latest = backups[0]
                print(f"  {Fore.CYAN}Latest backup: {latest['name']}")
                print(f"  {Fore.CYAN}Created: {latest['timestamp']}")
                print(f"  {Fore.CYAN}Size: {latest['size']} bytes")
            
            return True
        else:
            print_result("Backup Creation", False, "No backup file created")
            return False
            
    except Exception as e:
        print_result("Backup Creation", False, f"Error: {e}")
        return False

def test_registry_operations():
    """Test registry operations"""
    print_header("Registry Operations Tests")
    
    idm_logic = IDMLogic()
    
    # Test registry path detection
    try:
        paths = idm_logic.registry_paths
        print_result("Registry Path Detection", True, 
                    f"CLSID: {paths['clsid_hkcu']}")
        
        # Test CLSID key finding
        clsid_keys = idm_logic._find_idm_clsid_keys()
        print_result("CLSID Key Detection", True, 
                    f"Found {len(clsid_keys)} IDM CLSID key(s)")
        
        if clsid_keys:
            print(f"  {Fore.CYAN}Sample keys:")
            for key in clsid_keys[:3]:  # Show first 3
                print(f"    {Fore.WHITE}{key}")
        
        return True
    except Exception as e:
        print_result("Registry Operations", False, f"Error: {e}")
        return False

def test_internet_connection():
    """Test internet connectivity"""
    print_header("Internet Connection Test")
    
    idm_logic = IDMLogic()
    
    try:
        connected = idm_logic.check_internet_connection()
        print_result("Internet Connection", connected, 
                    "Connected" if connected else "Not connected")
        return connected
    except Exception as e:
        print_result("Internet Connection", False, f"Error: {e}")
        return False

def test_key_generation():
    """Test key and user info generation"""
    print_header("Key Generation Tests")
    
    idm_logic = IDMLogic()
    
    try:
        # Test serial key generation
        serial_key = idm_logic.generate_serial_key()
        valid_format = (len(serial_key) == 29 and 
                       serial_key.count('-') == 4)
        print_result("Serial Key Generation", valid_format, 
                    f"Generated: {serial_key}")
        
        # Test user info generation
        user_info = idm_logic.generate_user_info()
        valid_info = (all(key in user_info for key in ['first_name', 'last_name', 'email']) and
                     '@tonec.com' in user_info['email'])
        print_result("User Info Generation", valid_info, 
                    f"Email: {user_info['email']}")
        
        return valid_format and valid_info
    except Exception as e:
        print_result("Key Generation", False, f"Error: {e}")
        return False

def test_safe_operations():
    """Test safe operations that don't modify IDM"""
    print_header("Safe Operations Tests")
    
    idm_logic = IDMLogic()
    
    try:
        # Test process killing (safe if IDM not running)
        if not idm_logic.is_idm_running():
            result = idm_logic.kill_idm_process()
            print_result("Process Termination (Safe)", result, 
                        "No IDM process to kill")
        else:
            print_result("Process Termination (Skipped)", True, 
                        "IDM is running - skipped for safety")
        
        return True
    except Exception as e:
        print_result("Safe Operations", False, f"Error: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print_header("IDM Logic System Comprehensive Test")
    print(f"{Fore.YELLOW}Testing enhanced IDM logic system...")
    print(f"{Fore.YELLOW}This test will check functionality without modifying IDM.")
    
    # Configure logging for test
    logging.basicConfig(level=logging.INFO, 
                       format='%(levelname)s: %(message)s')
    
    test_results = []
    
    # Run tests
    test_results.append(("IDM Detection", test_idm_detection()))
    test_results.append(("Status Check", test_status_check()))
    test_results.append(("Backup System", test_backup_system()))
    test_results.append(("Registry Operations", test_registry_operations()))
    test_results.append(("Internet Connection", test_internet_connection()))
    test_results.append(("Key Generation", test_key_generation()))
    test_results.append(("Safe Operations", test_safe_operations()))
    
    # Summary
    print_header("Test Summary")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = f"{Fore.GREEN}✓" if result else f"{Fore.RED}✗"
        print(f"{status} {Fore.WHITE}{test_name}")
    
    print(f"\n{Fore.CYAN}Results: {Fore.GREEN}{passed}/{total} tests passed")
    
    if passed == total:
        print(f"{Fore.GREEN}🎉 All tests passed! The enhanced IDM logic system is working correctly.")
    else:
        print(f"{Fore.YELLOW}⚠ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_comprehensive_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Test interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Fore.RED}Unexpected error: {e}")
        sys.exit(1)

"""
Enhanced IDM Logic System
Provides improved activation and freeze functionality with better error handling
"""

import os
import sys
import subprocess
import winreg
import tempfile
import shutil
import logging
import json
import time
import random
import string
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

class IDMLogic:
    """Enhanced IDM Logic System with improved activation and freeze methods"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.idm_paths = self._get_idm_paths()
        self.registry_paths = self._get_registry_paths()
        self.backup_dir = Path.cwd() / "IDM_Backups"
        self.backup_dir.mkdir(exist_ok=True)
        
    def _get_idm_paths(self) -> List[str]:
        """Get possible IDM installation paths"""
        paths = []
        
        # Common installation paths
        program_files = os.environ.get('PROGRAMFILES', '')
        program_files_x86 = os.environ.get('PROGRAMFILES(X86)', '')
        
        if program_files:
            paths.append(os.path.join(program_files, 'Internet Download Manager', 'IDMan.exe'))
        if program_files_x86:
            paths.append(os.path.join(program_files_x86, 'Internet Download Manager', 'IDMan.exe'))
            
        # Try to get path from registry
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\DownloadManager") as key:
                exe_path, _ = winreg.QueryValueEx(key, "ExePath")
                if exe_path and os.path.exists(exe_path):
                    paths.insert(0, exe_path)
        except (FileNotFoundError, OSError):
            pass
            
        return paths
    
    def _get_registry_paths(self) -> Dict[str, str]:
        """Get registry paths based on system architecture"""
        import platform
        arch = platform.machine().lower()
        
        if arch in ['amd64', 'x86_64']:
            return {
                'clsid_hkcu': r'Software\Classes\Wow6432Node\CLSID',
                'clsid_hku': r'Software\Classes\Wow6432Node\CLSID',
                'hklm': r'SOFTWARE\Wow6432Node\Internet Download Manager',
                'download_manager': r'Software\DownloadManager'
            }
        else:
            return {
                'clsid_hkcu': r'Software\Classes\CLSID',
                'clsid_hku': r'Software\Classes\CLSID',
                'hklm': r'Software\Internet Download Manager',
                'download_manager': r'Software\DownloadManager'
            }
    
    def is_idm_installed(self) -> Tuple[bool, Optional[str]]:
        """Check if IDM is installed and return path if found"""
        for path in self.idm_paths:
            if os.path.exists(path):
                self.logger.info(f"IDM found at: {path}")
                return True, path
        
        self.logger.warning("IDM installation not found")
        return False, None
    
    def is_idm_running(self) -> bool:
        """Check if IDM process is running"""
        try:
            result = subprocess.run(
                ['tasklist', '/fi', 'imagename eq idman.exe'],
                capture_output=True, text=True, check=True
            )
            return 'idman.exe' in result.stdout.lower()
        except subprocess.CalledProcessError:
            return False
    
    def kill_idm_process(self) -> bool:
        """Terminate IDM process if running"""
        if not self.is_idm_running():
            return True
            
        try:
            subprocess.run(['taskkill', '/f', '/im', 'idman.exe'], 
                         check=True, capture_output=True)
            self.logger.info("IDM process terminated")
            time.sleep(2)  # Wait for process to fully terminate
            return True
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to terminate IDM process: {e}")
            return False
    
    def get_idm_status(self) -> Dict[str, Any]:
        """Get comprehensive IDM status information"""
        status = {
            'installed': False,
            'running': False,
            'version': None,
            'registration_status': 'unknown',
            'trial_status': 'unknown',
            'serial_key': None,
            'trial_data': None,
            'path': None
        }
        
        # Check installation
        installed, path = self.is_idm_installed()
        status['installed'] = installed
        status['path'] = path
        
        if not installed:
            return status
            
        # Check if running
        status['running'] = self.is_idm_running()
        
        # Get registry information
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              self.registry_paths['download_manager']) as key:
                
                # Get version
                try:
                    version, _ = winreg.QueryValueEx(key, "idmvers")
                    status['version'] = version
                except FileNotFoundError:
                    pass
                
                # Check for serial key (activation)
                try:
                    serial, _ = winreg.QueryValueEx(key, "Serial")
                    if serial:
                        status['registration_status'] = 'activated'
                        status['serial_key'] = serial
                except FileNotFoundError:
                    pass
                
                # Check for trial data
                try:
                    trial_data, _ = winreg.QueryValueEx(key, "tvfrdt")
                    if trial_data:
                        status['trial_status'] = 'trial'
                        status['trial_data'] = trial_data
                except FileNotFoundError:
                    pass
                    
        except FileNotFoundError:
            self.logger.warning("IDM registry keys not found")
        
        return status
    
    def create_backup(self) -> Optional[str]:
        """Create a backup of IDM registry settings"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"IDM_Backup_{timestamp}.reg"
        
        try:
            # Export HKCU DownloadManager key
            cmd = [
                'reg', 'export', 
                f'HKEY_CURRENT_USER\\{self.registry_paths["download_manager"]}',
                str(backup_file), '/y'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if backup_file.exists():
                self.logger.info(f"Backup created: {backup_file}")
                return str(backup_file)
            else:
                self.logger.error("Backup file was not created")
                return None
                
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to create backup: {e}")
            return None
    
    def restore_backup(self, backup_file: str) -> bool:
        """Restore IDM settings from backup"""
        if not os.path.exists(backup_file):
            self.logger.error(f"Backup file not found: {backup_file}")
            return False
            
        try:
            # Kill IDM if running
            self.kill_idm_process()
            
            # Import registry file
            cmd = ['reg', 'import', backup_file]
            subprocess.run(cmd, check=True, capture_output=True)
            
            self.logger.info(f"Backup restored from: {backup_file}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to restore backup: {e}")
            return False
    
    def generate_serial_key(self) -> str:
        """Generate a random serial key in IDM format"""
        # Generate 5 groups of 5 characters each
        groups = []
        for _ in range(5):
            group = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
            groups.append(group)
        
        return '-'.join(groups)
    
    def generate_user_info(self) -> Dict[str, str]:
        """Generate random user information for registration"""
        first_name = f"User{random.randint(1000, 9999)}"
        last_name = f"Name{random.randint(1000, 9999)}"
        email = f"{first_name.lower()}.{last_name.lower()}@tonec.com"
        
        return {
            'first_name': first_name,
            'last_name': last_name,
            'email': email
        }

    def reset_idm(self) -> bool:
        """Reset IDM activation and trial data"""
        self.logger.info("Starting IDM reset process")

        # Create backup before reset
        backup_file = self.create_backup()
        if not backup_file:
            self.logger.warning("Could not create backup before reset")

        # Kill IDM process
        if not self.kill_idm_process():
            self.logger.error("Could not terminate IDM process")
            return False

        success = True

        # Registry keys to delete
        keys_to_delete = [
            "FName", "LName", "Email", "Serial", "scansk",
            "tvfrdt", "radxcnt", "LstCheck", "ptrk_scdt", "LastCheckQU"
        ]

        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                              self.registry_paths['download_manager'],
                              0, winreg.KEY_ALL_ACCESS) as key:

                for value_name in keys_to_delete:
                    try:
                        winreg.DeleteValue(key, value_name)
                        self.logger.info(f"Deleted registry value: {value_name}")
                    except FileNotFoundError:
                        # Value doesn't exist, which is fine
                        pass
                    except Exception as e:
                        self.logger.error(f"Failed to delete {value_name}: {e}")
                        success = False

        except FileNotFoundError:
            self.logger.warning("DownloadManager registry key not found")
        except Exception as e:
            self.logger.error(f"Error accessing registry: {e}")
            success = False

        # Clean CLSID keys
        if not self._clean_clsid_keys():
            success = False

        # Add required registry key
        if not self._add_required_keys():
            success = False

        if success:
            self.logger.info("IDM reset completed successfully")
        else:
            self.logger.error("IDM reset completed with errors")

        return success

    def activate_idm(self) -> bool:
        """Activate IDM with generated serial key"""
        self.logger.info("Starting IDM activation process")

        # Check if IDM is installed
        installed, idm_path = self.is_idm_installed()
        if not installed:
            self.logger.error("IDM is not installed")
            return False

        # Create backup
        backup_file = self.create_backup()
        if not backup_file:
            self.logger.warning("Could not create backup before activation")

        # Kill IDM process
        if not self.kill_idm_process():
            self.logger.error("Could not terminate IDM process")
            return False

        # Reset first
        if not self.reset_idm():
            self.logger.error("Failed to reset IDM before activation")
            return False

        # Generate registration data
        user_info = self.generate_user_info()
        serial_key = self.generate_serial_key()

        # Apply registration
        success = self._apply_registration(user_info, serial_key)

        if success:
            # Test activation by triggering downloads
            if self._test_activation(idm_path):
                self.logger.info("IDM activation completed successfully")
                return True
            else:
                self.logger.warning("Activation applied but test failed")
                return False
        else:
            self.logger.error("Failed to apply activation")
            return False

    def freeze_trial(self) -> bool:
        """Freeze IDM trial period for lifetime"""
        self.logger.info("Starting IDM trial freeze process")

        # Check if IDM is installed
        installed, idm_path = self.is_idm_installed()
        if not installed:
            self.logger.error("IDM is not installed")
            return False

        # Create backup
        backup_file = self.create_backup()
        if not backup_file:
            self.logger.warning("Could not create backup before freeze")

        # Kill IDM process
        if not self.kill_idm_process():
            self.logger.error("Could not terminate IDM process")
            return False

        # Reset first
        if not self.reset_idm():
            self.logger.error("Failed to reset IDM before freeze")
            return False

        # Test with downloads to create registry keys
        if not self._test_activation(idm_path):
            self.logger.error("Failed to create necessary registry keys")
            return False

        # Lock CLSID keys to freeze trial
        if self._lock_clsid_keys():
            self.logger.info("IDM trial freeze completed successfully")
            return True
        else:
            self.logger.error("Failed to lock CLSID keys")
            return False

    def _apply_registration(self, user_info: Dict[str, str], serial_key: str) -> bool:
        """Apply registration information to registry"""
        try:
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER,
                                self.registry_paths['download_manager']) as key:

                winreg.SetValueEx(key, "FName", 0, winreg.REG_SZ, user_info['first_name'])
                winreg.SetValueEx(key, "LName", 0, winreg.REG_SZ, user_info['last_name'])
                winreg.SetValueEx(key, "Email", 0, winreg.REG_SZ, user_info['email'])
                winreg.SetValueEx(key, "Serial", 0, winreg.REG_SZ, serial_key)

                self.logger.info("Registration information applied successfully")
                return True

        except Exception as e:
            self.logger.error(f"Failed to apply registration: {e}")
            return False

    def _test_activation(self, idm_path: str) -> bool:
        """Test activation by triggering downloads"""
        test_urls = [
            "https://www.internetdownloadmanager.com/images/idm_box_min.png",
            "https://www.internetdownloadmanager.com/register/IDMlib/images/idman_logos.png",
            "https://www.internetdownloadmanager.com/pictures/idm_about.png"
        ]

        temp_dir = tempfile.mkdtemp()
        success = False

        try:
            for i, url in enumerate(test_urls):
                temp_file = os.path.join(temp_dir, f"test_{i}.png")

                # Start IDM download
                cmd = [
                    idm_path, '/n', '/d', url,
                    '/p', temp_dir, '/f', f"test_{i}.png"
                ]

                try:
                    subprocess.run(cmd, timeout=30, check=True)

                    # Wait for file to be created
                    for _ in range(20):
                        if os.path.exists(temp_file):
                            success = True
                            break
                        time.sleep(1)

                    if success:
                        break

                except (subprocess.TimeoutExpired, subprocess.CalledProcessError) as e:
                    self.logger.warning(f"Download test failed for {url}: {e}")
                    continue

            # Kill IDM process after test
            self.kill_idm_process()

        finally:
            # Clean up temp directory
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                self.logger.warning(f"Failed to clean temp directory: {e}")

        return success

    def _clean_clsid_keys(self) -> bool:
        """Clean IDM CLSID registry keys"""
        success = True

        # Get CLSID keys to clean
        clsid_keys = self._find_idm_clsid_keys()

        for key_path in clsid_keys:
            try:
                winreg.DeleteKey(winreg.HKEY_CURRENT_USER, key_path)
                self.logger.info(f"Deleted CLSID key: {key_path}")
            except FileNotFoundError:
                # Key doesn't exist, which is fine
                pass
            except Exception as e:
                self.logger.error(f"Failed to delete CLSID key {key_path}: {e}")
                success = False

        return success

    def _lock_clsid_keys(self) -> bool:
        """Lock IDM CLSID keys to freeze trial"""
        # This is a simplified version - the full implementation would use
        # Windows API calls to modify registry permissions

        clsid_keys = self._find_idm_clsid_keys()

        if len(clsid_keys) > 20:
            # Too many keys, delete instead of lock
            return self._clean_clsid_keys()

        # For now, we'll use a PowerShell script approach
        return self._lock_keys_with_powershell(clsid_keys)

    def _find_idm_clsid_keys(self) -> List[str]:
        """Find IDM-related CLSID keys in registry"""
        clsid_keys = []
        clsid_path = self.registry_paths['clsid_hkcu']

        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, clsid_path) as key:
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)

                        # Check if this looks like a GUID
                        if (subkey_name.startswith('{') and subkey_name.endswith('}')
                            and len(subkey_name) == 38):

                            # Check if it's IDM-related
                            if self._is_idm_clsid_key(clsid_path, subkey_name):
                                clsid_keys.append(f"{clsid_path}\\{subkey_name}")

                        i += 1
                    except OSError:
                        break

        except FileNotFoundError:
            self.logger.warning(f"CLSID path not found: {clsid_path}")

        return clsid_keys

    def _is_idm_clsid_key(self, clsid_path: str, subkey_name: str) -> bool:
        """Check if a CLSID key is related to IDM"""
        try:
            full_path = f"{clsid_path}\\{subkey_name}"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, full_path) as key:

                # Check for IDM-specific patterns
                try:
                    # Check default value
                    default_value, _ = winreg.QueryValueEx(key, "")
                    if default_value and (default_value.isdigit() or
                                        '+' in default_value or '=' in default_value):
                        return True
                except FileNotFoundError:
                    pass

                # Check for IDM-specific value names
                idm_patterns = ["MData", "Model", "scansk", "Therad"]
                i = 0
                while True:
                    try:
                        value_name, _, _ = winreg.EnumValue(key, i)
                        if any(pattern in value_name for pattern in idm_patterns):
                            return True
                        i += 1
                    except OSError:
                        break

                # Check if key has no values and no subkeys (empty IDM key)
                try:
                    winreg.EnumValue(key, 0)
                    has_values = True
                except OSError:
                    has_values = False

                try:
                    winreg.EnumKey(key, 0)
                    has_subkeys = True
                except OSError:
                    has_subkeys = False

                if not has_values and not has_subkeys:
                    return True

        except Exception as e:
            self.logger.debug(f"Error checking CLSID key {subkey_name}: {e}")

        return False

    def _lock_keys_with_powershell(self, clsid_keys: List[str]) -> bool:
        """Lock CLSID keys using PowerShell script"""
        if not clsid_keys:
            return True

        # Create PowerShell script for locking keys
        ps_script = '''
        $ErrorActionPreference = "SilentlyContinue"

        function Take-Permissions {
            param($regKey)

            try {
                $SID = New-Object System.Security.Principal.SecurityIdentifier('S-1-5-32-544')
                $Admin = ($SID.Translate([System.Security.Principal.NTAccount])).Value
                $AdminAccount = New-Object System.Security.Principal.NTAccount($Admin)

                $none = New-Object System.Security.Principal.SecurityIdentifier('S-1-0-0')
                $everyone = New-Object System.Security.Principal.SecurityIdentifier('S-1-1-0')

                $key = [Microsoft.Win32.Registry]::CurrentUser.OpenSubKey($regKey, 'ReadWriteSubTree', 'TakeOwnership')
                if ($key -eq $null) {
                    New-Item -Path "HKCU:\\$regKey" -Force | Out-Null
                    $key = [Microsoft.Win32.Registry]::CurrentUser.OpenSubKey($regKey, 'ReadWriteSubTree', 'TakeOwnership')
                }

                $acl = New-Object System.Security.AccessControl.RegistrySecurity
                $acl.SetOwner($none)
                $key.SetAccessControl($acl)

                $key = $key.OpenSubKey('', 'ReadWriteSubTree', 'ChangePermissions')
                $rule = New-Object System.Security.AccessControl.RegistryAccessRule($everyone, 'FullControl', 'Deny')
                $acl.ResetAccessRule($rule)
                $key.SetAccessControl($acl)

                Write-Host "Locked: $regKey"
                return $true
            }
            catch {
                Write-Host "Failed to lock: $regKey - $($_.Exception.Message)"
                return $false
            }
        }

        $keys = @(
        '''

        # Add keys to script
        for key in clsid_keys:
            # Remove HKEY_CURRENT_USER\ prefix if present
            clean_key = key.replace('HKEY_CURRENT_USER\\', '').replace('Software\\Classes\\', 'Software\\Classes\\')
            ps_script += f'    "{clean_key}",\n'

        ps_script += '''
        )

        $success = $true
        foreach ($key in $keys) {
            if (-not (Take-Permissions $key)) {
                $success = $false
            }
        }

        if ($success) {
            Write-Host "All keys locked successfully"
            exit 0
        } else {
            Write-Host "Some keys failed to lock"
            exit 1
        }
        '''

        # Write script to temp file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ps1', delete=False) as f:
            f.write(ps_script)
            script_path = f.name

        try:
            # Execute PowerShell script
            cmd = [
                'powershell.exe', '-ExecutionPolicy', 'Bypass',
                '-File', script_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                self.logger.info("CLSID keys locked successfully")
                return True
            else:
                self.logger.error(f"Failed to lock keys: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Error executing PowerShell script: {e}")
            return False
        finally:
            # Clean up script file
            try:
                os.unlink(script_path)
            except Exception:
                pass

    def _add_required_keys(self) -> bool:
        """Add required registry keys for IDM"""
        try:
            # Add AdvIntDriverEnabled2 key to HKLM
            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE,
                                self.registry_paths['hklm']) as key:
                winreg.SetValueEx(key, "AdvIntDriverEnabled2", 0, winreg.REG_DWORD, 1)
                self.logger.info("Added AdvIntDriverEnabled2 registry key")
                return True

        except PermissionError:
            self.logger.warning("No permission to write to HKLM, trying alternative method")
            # Try using reg.exe command
            try:
                cmd = [
                    'reg', 'add', f'HKLM\\{self.registry_paths["hklm"]}',
                    '/v', 'AdvIntDriverEnabled2', '/t', 'REG_DWORD', '/d', '1', '/f'
                ]
                subprocess.run(cmd, check=True, capture_output=True)
                self.logger.info("Added AdvIntDriverEnabled2 using reg.exe")
                return True
            except subprocess.CalledProcessError as e:
                self.logger.error(f"Failed to add registry key: {e}")
                return False
        except Exception as e:
            self.logger.error(f"Error adding required keys: {e}")
            return False

    def check_internet_connection(self) -> bool:
        """Check if internet connection is available"""
        test_hosts = [
            "internetdownloadmanager.com",
            "google.com",
            "microsoft.com"
        ]

        for host in test_hosts:
            try:
                result = subprocess.run(
                    ['ping', '-n', '1', host],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    self.logger.info(f"Internet connection verified via {host}")
                    return True
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                continue

        self.logger.warning("No internet connection detected")
        return False

    def get_available_backups(self) -> List[Dict[str, str]]:
        """Get list of available backup files"""
        backups = []

        if not self.backup_dir.exists():
            return backups

        for backup_file in self.backup_dir.glob("IDM_Backup_*.reg"):
            try:
                # Extract timestamp from filename
                timestamp_str = backup_file.stem.replace("IDM_Backup_", "")
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")

                backups.append({
                    'file': str(backup_file),
                    'name': backup_file.name,
                    'timestamp': timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    'size': backup_file.stat().st_size
                })
            except (ValueError, OSError) as e:
                self.logger.warning(f"Invalid backup file {backup_file}: {e}")

        # Sort by timestamp (newest first)
        backups.sort(key=lambda x: x['timestamp'], reverse=True)
        return backups

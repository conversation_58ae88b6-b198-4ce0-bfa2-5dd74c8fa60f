"""
IDM Activation Troubleshooter and Advanced Fix Tool
Diagnoses and fixes IDM activation failures
"""

import os
import sys
import subprocess
import winreg
import time
import logging
from pathlib import Path
from colorama import Fore, init, just_fix_windows_console

# Initialize colorama
init(autoreset=True, convert=True, strip=False)
just_fix_windows_console()

try:
    from idm_logic import IDMLogic
except ImportError:
    print(f"{Fore.RED}Error: idm_logic.py not found!")
    print(f"{Fore.YELLOW}Please ensure all files are in the same directory.")
    input("Press Enter to exit...")
    sys.exit(1)

class IDMTroubleshooter:
    def __init__(self):
        self.idm_logic = IDMLogic()
        self.issues_found = []
        self.fixes_applied = []
        
    def print_header(self):
        print(f"{Fore.CYAN}{'='*70}")
        print(f"{Fore.WHITE}IDM Activation Troubleshooter & Advanced Fix Tool".center(70))
        print(f"{Fore.YELLOW}Diagnoses and fixes IDM activation failures".center(70))
        print(f"{Fore.CYAN}{'='*70}")
    
    def diagnose_system(self):
        """Comprehensive system diagnosis"""
        print(f"\n{Fore.CYAN}🔍 Running Comprehensive System Diagnosis...")
        print(f"{Fore.WHITE}{'='*50}")
        
        # Check 1: IDM Installation
        installed, path = self.idm_logic.is_idm_installed()
        if not installed:
            self.issues_found.append("IDM not installed")
            print(f"{Fore.RED}❌ IDM is not installed")
            return False
        else:
            print(f"{Fore.GREEN}✅ IDM found at: {path}")
        
        # Check 2: IDM Process Status
        running = self.idm_logic.is_idm_running()
        if running:
            self.issues_found.append("IDM process running")
            print(f"{Fore.YELLOW}⚠ IDM process is running (may interfere)")
        else:
            print(f"{Fore.GREEN}✅ IDM process not running")
        
        # Check 3: Internet Connection
        connected = self.idm_logic.check_internet_connection()
        if not connected:
            self.issues_found.append("No internet connection")
            print(f"{Fore.RED}❌ No internet connection")
        else:
            print(f"{Fore.GREEN}✅ Internet connection available")
        
        # Check 4: Registry Status
        status = self.idm_logic.get_idm_status()
        print(f"\n{Fore.CYAN}📊 Current IDM Status:")
        
        if status['registration_status'] == 'activated':
            print(f"{Fore.GREEN}✅ IDM appears to be activated")
            if status['serial_key']:
                masked = status['serial_key'][:5] + "***" + status['serial_key'][-5:]
                print(f"{Fore.CYAN}   Serial: {masked}")
        elif status['trial_status'] == 'trial':
            print(f"{Fore.YELLOW}⚠ IDM is in trial mode")
            self.issues_found.append("IDM in trial mode")
        else:
            print(f"{Fore.RED}❌ IDM status unknown")
            self.issues_found.append("Unknown IDM status")
        
        # Check 5: Registry Corruption
        corruption_found = self._check_registry_corruption()
        if corruption_found:
            self.issues_found.append("Registry corruption detected")
            print(f"{Fore.RED}❌ Registry corruption detected")
        else:
            print(f"{Fore.GREEN}✅ Registry appears clean")
        
        # Check 6: CLSID Issues
        clsid_issues = self._check_clsid_issues()
        if clsid_issues:
            self.issues_found.append(f"CLSID issues ({clsid_issues} problematic keys)")
            print(f"{Fore.YELLOW}⚠ Found {clsid_issues} problematic CLSID keys")
        else:
            print(f"{Fore.GREEN}✅ CLSID keys appear clean")
        
        # Check 7: Admin Privileges
        admin_check = self._check_admin_privileges()
        if not admin_check:
            self.issues_found.append("No admin privileges")
            print(f"{Fore.YELLOW}⚠ Not running as administrator")
        else:
            print(f"{Fore.GREEN}✅ Running with admin privileges")
        
        return True
    
    def _check_registry_corruption(self):
        """Check for registry corruption that might cause activation failure"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              self.idm_logic.registry_paths['download_manager']) as key:
                
                # Check for conflicting entries
                conflicting_values = []
                i = 0
                while True:
                    try:
                        value_name, value_data, value_type = winreg.EnumValue(key, i)
                        
                        # Check for suspicious values that might cause fake serial detection
                        if value_name in ['Serial', 'scansk'] and not value_data:
                            conflicting_values.append(value_name)
                        elif value_name == 'Serial' and len(str(value_data)) != 29:
                            conflicting_values.append(f"{value_name} (invalid format)")
                        
                        i += 1
                    except OSError:
                        break
                
                return len(conflicting_values) > 0
                
        except FileNotFoundError:
            return False
        except Exception:
            return True
    
    def _check_clsid_issues(self):
        """Check for CLSID issues that might cause problems"""
        try:
            clsid_keys = self.idm_logic._find_idm_clsid_keys()
            
            # Too many CLSID keys can cause issues
            if len(clsid_keys) > 50:
                return len(clsid_keys)
            
            return 0
        except Exception:
            return 0
    
    def _check_admin_privileges(self):
        """Check if running with admin privileges"""
        try:
            result = subprocess.run(['net', 'session'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False
    
    def show_diagnosis_summary(self):
        """Show diagnosis summary and recommendations"""
        print(f"\n{Fore.CYAN}📋 Diagnosis Summary:")
        print(f"{Fore.WHITE}{'='*50}")
        
        if not self.issues_found:
            print(f"{Fore.GREEN}✅ No major issues detected")
            print(f"{Fore.CYAN}   System appears ready for activation")
        else:
            print(f"{Fore.YELLOW}⚠ Issues found: {len(self.issues_found)}")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"{Fore.RED}   {i}. {issue}")
        
        print(f"\n{Fore.CYAN}💡 Recommended Actions:")
        
        if "IDM not installed" in self.issues_found:
            print(f"{Fore.YELLOW}   1. Install IDM from: https://www.internetdownloadmanager.com/download.html")
            return
        
        if "No internet connection" in self.issues_found:
            print(f"{Fore.YELLOW}   1. Check your internet connection")
            print(f"{Fore.YELLOW}   2. Try again when connection is available")
        
        if "IDM process running" in self.issues_found:
            print(f"{Fore.YELLOW}   1. Close IDM completely before activation")
        
        if "Registry corruption detected" in self.issues_found:
            print(f"{Fore.YELLOW}   1. Use the 'Advanced Registry Fix' option")
        
        if any("CLSID" in issue for issue in self.issues_found):
            print(f"{Fore.YELLOW}   1. Use the 'CLSID Cleanup' option")
        
        if "No admin privileges" in self.issues_found:
            print(f"{Fore.YELLOW}   1. Run this tool as Administrator")
        
        print(f"{Fore.GREEN}   2. Try the 'Freeze Trial' method (most reliable)")
        print(f"{Fore.GREEN}   3. Use the 'Fix Fake Serial' if you see fake serial errors")
    
    def apply_comprehensive_fix(self):
        """Apply comprehensive fix based on diagnosis"""
        print(f"\n{Fore.CYAN}🔧 Applying Comprehensive Fix...")
        print(f"{Fore.WHITE}{'='*50}")
        
        success_count = 0
        total_fixes = 0
        
        # Fix 1: Kill IDM process
        if "IDM process running" in self.issues_found:
            total_fixes += 1
            print(f"{Fore.YELLOW}🔄 Terminating IDM process...")
            if self.idm_logic.kill_idm_process():
                print(f"{Fore.GREEN}✅ IDM process terminated")
                self.fixes_applied.append("IDM process terminated")
                success_count += 1
            else:
                print(f"{Fore.RED}❌ Failed to terminate IDM process")
        
        # Fix 2: Comprehensive reset
        total_fixes += 1
        print(f"{Fore.YELLOW}🔄 Performing comprehensive reset...")
        if self.idm_logic._comprehensive_reset():
            print(f"{Fore.GREEN}✅ Comprehensive reset completed")
            self.fixes_applied.append("Comprehensive reset")
            success_count += 1
        else:
            print(f"{Fore.RED}❌ Comprehensive reset failed")
        
        # Fix 3: Enhanced activation
        total_fixes += 1
        print(f"{Fore.YELLOW}🔄 Applying enhanced activation...")
        user_info = self.idm_logic.generate_user_info()
        serial_key = self.idm_logic._generate_enhanced_serial_key()
        
        if self.idm_logic._apply_enhanced_registration(user_info, serial_key):
            print(f"{Fore.GREEN}✅ Enhanced activation applied")
            print(f"{Fore.CYAN}   Serial: {serial_key}")
            self.fixes_applied.append("Enhanced activation")
            success_count += 1
        else:
            print(f"{Fore.RED}❌ Enhanced activation failed")
        
        # Fix 4: Validation
        total_fixes += 1
        print(f"{Fore.YELLOW}🔄 Validating fix...")
        if self.idm_logic._validate_activation():
            print(f"{Fore.GREEN}✅ Activation validated successfully")
            self.fixes_applied.append("Activation validated")
            success_count += 1
        else:
            print(f"{Fore.YELLOW}⚠ Validation uncertain - may still work")
        
        return success_count, total_fixes
    
    def run_troubleshooter(self):
        """Main troubleshooter workflow"""
        self.print_header()
        
        # Step 1: Diagnosis
        if not self.diagnose_system():
            print(f"\n{Fore.RED}❌ Critical issues found. Please address them first.")
            return False
        
        # Step 2: Show summary
        self.show_diagnosis_summary()
        
        # Step 3: Ask for fix
        if self.issues_found:
            print(f"\n{Fore.YELLOW}🔧 Would you like to apply automatic fixes?")
            choice = input(f"{Fore.GREEN}Apply comprehensive fix? (Y/n): ").lower()
            
            if choice != 'n':
                success_count, total_fixes = self.apply_comprehensive_fix()
                
                print(f"\n{Fore.CYAN}📊 Fix Results:")
                print(f"{Fore.WHITE}{'='*50}")
                print(f"{Fore.GREEN}✅ Successful fixes: {success_count}/{total_fixes}")
                
                if self.fixes_applied:
                    print(f"{Fore.CYAN}Applied fixes:")
                    for fix in self.fixes_applied:
                        print(f"{Fore.WHITE}   • {fix}")
                
                if success_count == total_fixes:
                    print(f"\n{Fore.GREEN}🎉 All fixes applied successfully!")
                    print(f"{Fore.CYAN}Try using IDM now - the activation should work.")
                elif success_count > 0:
                    print(f"\n{Fore.YELLOW}⚠ Partial success. Try the Freeze Trial method.")
                else:
                    print(f"\n{Fore.RED}❌ Fixes failed. Try running as Administrator.")
        else:
            print(f"\n{Fore.GREEN}✅ System appears healthy. Try activation again.")
        
        return True

def main():
    troubleshooter = IDMTroubleshooter()
    
    try:
        troubleshooter.run_troubleshooter()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Troubleshooting interrupted by user.")
    except Exception as e:
        print(f"\n{Fore.RED}Error during troubleshooting: {e}")
        logging.error(f"Troubleshooter error: {e}")
    
    input(f"\n{Fore.YELLOW}Press Enter to exit...")

if __name__ == "__main__":
    main()

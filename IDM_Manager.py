import subprocess
import os
import sys
import time
import webbrowser
import logging
from colorama import Fore, Style, Back, init, just_fix_windows_console
from idm_logic import IDMLogic

# Initialize colorama for Windows console
init(autoreset=True, convert=True, strip=False)
just_fix_windows_console()

VERSION = "2.1"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('idm_manager.log'),
        logging.StreamHandler()
    ]
)

# Initialize IDM Logic
idm_logic = IDMLogic()

def clear_screen():
    """Clear the console screen."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the application header."""
    clear_screen()
    print(Fore.CYAN + "=" * 70)
    print(Fore.CYAN + "=" + Fore.WHITE + Back.BLUE + " IDM FREEZER & ACTIVATION TOOL ".center(68) + Fore.CYAN + "=")
    print(Fore.CYAN + "=" + Fore.YELLOW + f" Version {VERSION} - Enhanced Logic System ".center(68) + Fore.CYAN + "=")
    print(Fore.CYAN + "=" * 70)
    print(Fore.MAGENTA + """
     ██╗██████╗ ███╗   ███╗    ███████╗██████╗ ███████╗███████╗███████╗███████╗██████╗
     ██║██╔══██╗████╗ ████║    ██╔════╝██╔══██╗██╔════╝██╔════╝╚══███╔╝██╔════╝██╔══██╗
     ██║██║  ██║██╔████╔██║    █████╗  ██████╔╝█████╗  █████╗    ███╔╝ █████╗  ██████╔╝
     ██║██║  ██║██║╚██╔╝██║    ██╔══╝  ██╔══██╗██╔══╝  ██╔══╝   ███╔╝  ██╔══╝  ██╔══██╗
     ██║██████╔╝██║ ╚═╝ ██║    ██║     ██║  ██║███████╗███████╗███████╗███████╗██║  ██║
     ╚═╝╚═════╝ ╚═╝     ╚═╝    ╚═╝     ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝╚══════╝╚═╝  ╚═╝
                                                            ENHANCED v2.1 - 2025
    """)
    print(Fore.CYAN + "=" * 70)

def print_menu():
    """Print the main menu options."""
    print(Fore.GREEN + "\nMAIN MENU:")
    print(Fore.WHITE + "=" * 70)
    print(Fore.YELLOW + " [1]" + Fore.WHITE + " Activate IDM" + Fore.CYAN + " (Anti-Fake Serial)")
    print(Fore.YELLOW + " [2]" + Fore.WHITE + " Freeze Trial" + Fore.GREEN + " (Recommended - Permanent)")
    print(Fore.YELLOW + " [3]" + Fore.WHITE + " Fix Fake Serial Issue" + Fore.RED + " (Specialized Fix)")
    print(Fore.YELLOW + " [4]" + Fore.WHITE + " Reset Activation/Trial" + Fore.CYAN + " (Clean Slate)")
    print(Fore.YELLOW + " [5]" + Fore.WHITE + " Check IDM Status" + Fore.CYAN + " (Comprehensive)")
    print(Fore.YELLOW + " [6]" + Fore.WHITE + " Backup/Restore Settings" + Fore.CYAN + " (Enhanced)")
    print(Fore.WHITE + "=" * 70)
    print(Fore.YELLOW + " [7]" + Fore.WHITE + " Download IDM")
    print(Fore.YELLOW + " [8]" + Fore.WHITE + " Visit GitHub Repository")
    print(Fore.YELLOW + " [9]" + Fore.WHITE + " Run System Test")
    print(Fore.YELLOW + " [0]" + Fore.WHITE + " Exit")
    print(Fore.WHITE + "=" * 70)

def show_progress(message, duration=3):
    """Show a progress bar with the given message."""
    print(Fore.CYAN + f"\n{message}")
    print(Fore.YELLOW + "Progress: ", end="")
    for _ in range(50):
        time.sleep(duration/50)
        print(Fore.GREEN + "█", end="", flush=True)
    print(Fore.GREEN + " Done!")

def activate_idm():
    """Activate IDM using enhanced logic with fake serial handling"""
    print(Fore.CYAN + "\n🚀 Starting Enhanced IDM Activation...")
    print(Fore.YELLOW + "This method handles fake serial number issues automatically.")

    # Check if IDM is installed
    installed, path = idm_logic.is_idm_installed()
    if not installed:
        print(Fore.RED + "❌ IDM is not installed on this system.")
        print(Fore.YELLOW + "📥 You can download it from https://www.internetdownloadmanager.com/download.html")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return

    print(Fore.GREEN + f"✓ IDM found at: {path}")

    # Check internet connection
    if not idm_logic.check_internet_connection():
        print(Fore.RED + "❌ Internet connection required for activation.")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return

    # Warning about fake serial issue
    print(Fore.YELLOW + "\n⚠ Note: This activation method is designed to handle:")
    print(Fore.CYAN + "  • Fake serial number detection")
    print(Fore.CYAN + "  • Previously used serial keys")
    print(Fore.CYAN + "  • Registry corruption issues")

    confirm = input(Fore.GREEN + "\nProceed with enhanced activation? (Y/n): ").lower()
    if confirm == 'n':
        return

    show_progress("Performing comprehensive reset and activation", 8)

    try:
        success = idm_logic.activate_idm()

        if success:
            print(Fore.GREEN + "\n🎉 IDM Activation completed successfully!")
            print(Fore.CYAN + "✓ Your IDM is now activated with a valid serial key.")
            print(Fore.CYAN + "✓ Fake serial detection has been bypassed.")
            print(Fore.YELLOW + "✓ Activation will persist through IDM updates.")
            print(Fore.GREEN + "✓ No more fake serial number warnings!")
        else:
            print(Fore.RED + "\n❌ IDM Activation failed!")
            print(Fore.YELLOW + "💡 The system automatically tried fallback methods.")
            print(Fore.CYAN + "🔄 Recommendation: Try the Freeze Trial option instead.")
            print(Fore.CYAN + "   Freeze Trial is often more reliable for problematic systems.")

    except Exception as e:
        print(Fore.RED + f"\n❌ An error occurred during activation: {e}")
        print(Fore.YELLOW + "💡 This might be due to:")
        print(Fore.CYAN + "  • IDM version incompatibility")
        print(Fore.CYAN + "  • System security restrictions")
        print(Fore.CYAN + "  • Antivirus interference")
        print(Fore.GREEN + "🔄 Try running as administrator or use Freeze Trial instead.")
        logging.error(f"Activation error: {e}")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def freeze_trial():
    """Freeze IDM trial using enhanced logic"""
    print(Fore.CYAN + "\nStarting IDM Trial Freeze...")
    
    # Check if IDM is installed
    installed, path = idm_logic.is_idm_installed()
    if not installed:
        print(Fore.RED + "IDM is not installed on this system.")
        print(Fore.YELLOW + "You can download it from https://www.internetdownloadmanager.com/download.html")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return
    
    print(Fore.GREEN + f"IDM found at: {path}")
    
    # Check internet connection
    if not idm_logic.check_internet_connection():
        print(Fore.RED + "Internet connection required for trial freeze.")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return
    
    show_progress("Freezing IDM Trial", 5)
    
    try:
        success = idm_logic.freeze_trial()
        
        if success:
            print(Fore.GREEN + "\n🎉 IDM Trial freeze completed successfully!")
            print(Fore.CYAN + "✓ Your IDM trial period is now frozen for lifetime.")
            print(Fore.CYAN + "✓ Trial will persist through IDM updates.")
            print(Fore.YELLOW + "⚠ Note: If IDM shows registration popup, reinstall IDM.")
        else:
            print(Fore.RED + "\n❌ IDM Trial freeze failed!")
            
    except Exception as e:
        print(Fore.RED + f"\n❌ An error occurred during trial freeze: {e}")
        logging.error(f"Trial freeze error: {e}")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def fix_fake_serial_issue():
    """Specialized function to fix fake serial number issues"""
    print(Fore.RED + "\n🔧 IDM Fake Serial Number Fix")
    print(Fore.YELLOW + "=" * 60)
    print(Fore.WHITE + "This specialized tool fixes the common IDM error:")
    print(Fore.RED + "  'Internet Download Manager has been registered with")
    print(Fore.RED + "   a fake serial number or the serial number...'")
    print(Fore.YELLOW + "=" * 60)

    # Check if IDM is installed
    installed, path = idm_logic.is_idm_installed()
    if not installed:
        print(Fore.RED + "❌ IDM is not installed on this system.")
        print(Fore.YELLOW + "📥 You can download it from https://www.internetdownloadmanager.com/download.html")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return

    print(Fore.GREEN + f"✓ IDM found at: {path}")

    # Check internet connection
    if not idm_logic.check_internet_connection():
        print(Fore.RED + "❌ Internet connection required for this fix.")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return

    print(Fore.CYAN + "\n🔍 This fix will:")
    print(Fore.WHITE + "  1. Perform comprehensive registry cleanup")
    print(Fore.WHITE + "  2. Remove all traces of fake serial detection")
    print(Fore.WHITE + "  3. Apply enhanced activation method")
    print(Fore.WHITE + "  4. Validate the fix with test downloads")

    print(Fore.YELLOW + "\n⚠ Important Notes:")
    print(Fore.CYAN + "  • This will create a backup before making changes")
    print(Fore.CYAN + "  • IDM will be temporarily closed during the process")
    print(Fore.CYAN + "  • The process may take 1-2 minutes to complete")

    confirm = input(Fore.GREEN + "\nProceed with fake serial fix? (Y/n): ").lower()
    if confirm == 'n':
        return

    show_progress("Fixing fake serial issue", 10)

    try:
        # Use the comprehensive reset and enhanced activation
        print(Fore.CYAN + "\n🔄 Step 1: Comprehensive cleanup...")
        success = idm_logic._comprehensive_reset()

        if success:
            print(Fore.GREEN + "✓ Cleanup completed successfully")

            print(Fore.CYAN + "\n🔑 Step 2: Applying enhanced activation...")
            user_info = idm_logic.generate_user_info()
            serial_key = idm_logic._generate_enhanced_serial_key()

            activation_success = idm_logic._apply_enhanced_registration(user_info, serial_key)

            if activation_success:
                print(Fore.GREEN + "✓ Enhanced activation applied")

                print(Fore.CYAN + "\n🧪 Step 3: Validating fix...")
                if idm_logic._validate_activation():
                    print(Fore.GREEN + "\n🎉 Fake Serial Issue Fixed Successfully!")
                    print(Fore.CYAN + "✓ IDM is now properly activated")
                    print(Fore.CYAN + "✓ Fake serial detection bypassed")
                    print(Fore.CYAN + "✓ No more error messages")
                    print(Fore.YELLOW + "✓ Activation will persist through IDM updates")

                    # Show the new serial key (masked)
                    masked_serial = serial_key[:5] + "***" + serial_key[-5:]
                    print(Fore.GREEN + f"🔑 New Serial Key: {masked_serial}")
                else:
                    print(Fore.YELLOW + "\n⚠ Fix applied but validation uncertain")
                    print(Fore.CYAN + "Try restarting IDM to see if the issue is resolved")
            else:
                print(Fore.RED + "\n❌ Failed to apply enhanced activation")
                print(Fore.YELLOW + "💡 Try the Freeze Trial option instead")
        else:
            print(Fore.RED + "\n❌ Cleanup failed")
            print(Fore.YELLOW + "💡 Try running as administrator")

    except Exception as e:
        print(Fore.RED + f"\n❌ An error occurred during the fix: {e}")
        print(Fore.YELLOW + "💡 Possible solutions:")
        print(Fore.CYAN + "  • Run as administrator")
        print(Fore.CYAN + "  • Temporarily disable antivirus")
        print(Fore.CYAN + "  • Try the Freeze Trial option instead")
        logging.error(f"Fake serial fix error: {e}")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def reset_idm():
    """Reset IDM using enhanced logic"""
    print(Fore.CYAN + "\nStarting IDM Reset...")
    print(Fore.YELLOW + "⚠ This will remove all activation and trial data.")
    
    confirm = input(Fore.YELLOW + "Continue? (y/N): ").lower()
    if confirm != 'y':
        print(Fore.CYAN + "Reset cancelled.")
        input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
        return
    
    show_progress("Resetting IDM", 3)
    
    try:
        success = idm_logic.reset_idm()
        
        if success:
            print(Fore.GREEN + "\n✅ IDM Reset completed successfully!")
            print(Fore.CYAN + "✓ All activation and trial data has been cleared.")
            print(Fore.CYAN + "✓ Backup created before reset.")
        else:
            print(Fore.RED + "\n⚠ IDM Reset completed with some errors!")
            print(Fore.YELLOW + "Check the log file for details.")
            
    except Exception as e:
        print(Fore.RED + f"\n❌ An error occurred during reset: {e}")
        logging.error(f"Reset error: {e}")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def check_idm_status():
    """Check the current status of IDM installation using enhanced logic."""
    print(Fore.CYAN + "\nChecking IDM status...")
    
    try:
        status = idm_logic.get_idm_status()
        
        print(Fore.GREEN + "\n📊 IDM Status Report:")
        print(Fore.WHITE + "=" * 60)
        
        # Installation status
        if status['installed']:
            print(Fore.GREEN + f"✓ IDM is installed at: {status['path']}")
            if status['version']:
                print(Fore.CYAN + f"  📦 Version: {status['version']}")
        else:
            print(Fore.RED + "✗ IDM is not installed")
            print(Fore.YELLOW + "  📥 Download from: https://www.internetdownloadmanager.com/download.html")
            print(Fore.WHITE + "=" * 60)
            input(Fore.YELLOW + "\nPress Enter to return to the main menu...")
            return
        
        # Running status
        if status['running']:
            print(Fore.YELLOW + "⚠ IDM process is currently running")
        else:
            print(Fore.CYAN + "○ IDM process is not running")
        
        # Registration status
        if status['registration_status'] == 'activated':
            print(Fore.GREEN + "✓ IDM is activated with serial key")
            if status['serial_key']:
                # Show only first and last 5 characters for privacy
                masked_serial = status['serial_key'][:5] + "***" + status['serial_key'][-5:]
                print(Fore.CYAN + f"  🔑 Serial: {masked_serial}")
        elif status['trial_status'] == 'trial':
            print(Fore.YELLOW + "⚠ IDM is in trial mode")
            if status['trial_data']:
                print(Fore.CYAN + f"  📅 Trial data: {status['trial_data']}")
        else:
            print(Fore.RED + "? IDM status could not be determined")
        
        print(Fore.WHITE + "=" * 60)
        
        # Additional information
        if status['installed']:
            print(Fore.CYAN + "\n📋 Additional Information:")
            
            # Check for backups
            backups = idm_logic.get_available_backups()
            if backups:
                print(Fore.GREEN + f"✓ {len(backups)} backup(s) available")
                latest_backup = backups[0]
                print(Fore.CYAN + f"  📁 Latest backup: {latest_backup['timestamp']}")
            else:
                print(Fore.YELLOW + "⚠ No backups found")
            
            # Check internet connection
            if idm_logic.check_internet_connection():
                print(Fore.GREEN + "✓ Internet connection available")
            else:
                print(Fore.RED + "✗ No internet connection")
        
    except Exception as e:
        print(Fore.RED + f"❌ Error checking IDM status: {e}")
        logging.error(f"Status check error: {e}")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def backup_restore_idm():
    """Enhanced backup or restore IDM settings."""
    while True:
        clear_screen()
        print_header()
        print(Fore.GREEN + "\n💾 BACKUP/RESTORE IDM SETTINGS:")
        print(Fore.WHITE + "=" * 70)
        print(Fore.YELLOW + " [1]" + Fore.WHITE + " Backup IDM Settings" + Fore.CYAN + " (Create Backup)")
        print(Fore.YELLOW + " [2]" + Fore.WHITE + " Restore IDM Settings" + Fore.CYAN + " (From Backup)")
        print(Fore.YELLOW + " [3]" + Fore.WHITE + " View Available Backups" + Fore.CYAN + " (List All)")
        print(Fore.YELLOW + " [0]" + Fore.WHITE + " Return to Main Menu")
        print(Fore.WHITE + "=" * 70)

        choice = input(Fore.GREEN + "\nEnter your choice: ")

        if choice == "1":
            # Backup IDM settings
            print(Fore.CYAN + "\n📦 Creating IDM settings backup...")
            show_progress("Backing up IDM settings", 3)

            try:
                backup_file = idm_logic.create_backup()

                if backup_file:
                    print(Fore.GREEN + f"\n✅ Backup completed successfully!")
                    print(Fore.CYAN + f"📁 Backup saved to: {backup_file}")
                else:
                    print(Fore.RED + "\n❌ Backup failed!")

            except Exception as e:
                print(Fore.RED + f"\n❌ Error during backup: {e}")
                logging.error(f"Backup error: {e}")

            input(Fore.YELLOW + "\nPress Enter to continue...")

        elif choice == "2":
            # Restore IDM settings
            print(Fore.CYAN + "\n🔍 Looking for available backups...")

            try:
                backups = idm_logic.get_available_backups()

                if not backups:
                    print(Fore.RED + "\n❌ No backup files found!")
                    input(Fore.YELLOW + "\nPress Enter to continue...")
                    continue

                print(Fore.GREEN + f"\n📋 Found {len(backups)} backup file(s):")
                print(Fore.WHITE + "=" * 70)

                for i, backup in enumerate(backups, 1):
                    size_mb = backup['size'] / (1024 * 1024)
                    print(Fore.YELLOW + f" [{i}]" + Fore.WHITE + f" {backup['name']}")
                    print(Fore.CYAN + f"     📅 Created: {backup['timestamp']} | 📏 Size: {size_mb:.2f} MB")

                print(Fore.WHITE + "=" * 70)

                try:
                    file_choice = int(input(Fore.GREEN + "\nEnter the number of the file to restore (0 to cancel): "))
                    if file_choice == 0:
                        continue

                    if 1 <= file_choice <= len(backups):
                        selected_backup = backups[file_choice-1]

                        print(Fore.YELLOW + f"\n🔄 Restoring from: {selected_backup['name']}")
                        print(Fore.RED + "⚠ Warning: This will overwrite current IDM settings!")
                        confirm = input(Fore.YELLOW + "Continue? (y/N): ").lower()

                        if confirm == 'y':
                            show_progress("Restoring IDM settings", 3)

                            success = idm_logic.restore_backup(selected_backup['file'])

                            if success:
                                print(Fore.GREEN + "\n✅ Restore completed successfully!")
                            else:
                                print(Fore.RED + "\n❌ Restore failed!")
                        else:
                            print(Fore.CYAN + "Restore cancelled.")
                    else:
                        print(Fore.RED + "\n❌ Invalid selection!")

                except ValueError:
                    print(Fore.RED + "\n❌ Please enter a valid number!")

            except Exception as e:
                print(Fore.RED + f"\n❌ Error during restore: {e}")
                logging.error(f"Restore error: {e}")

            input(Fore.YELLOW + "\nPress Enter to continue...")

        elif choice == "3":
            # View available backups
            print(Fore.CYAN + "\n🔍 Scanning for backup files...")

            try:
                backups = idm_logic.get_available_backups()

                if not backups:
                    print(Fore.RED + "\n❌ No backup files found!")
                else:
                    print(Fore.GREEN + f"\n📋 Found {len(backups)} backup file(s):")
                    print(Fore.WHITE + "=" * 70)

                    total_size = 0
                    for i, backup in enumerate(backups, 1):
                        size_mb = backup['size'] / (1024 * 1024)
                        total_size += backup['size']

                        print(Fore.YELLOW + f" {i:2d}." + Fore.WHITE + f" {backup['name']}")
                        print(Fore.CYAN + f"     📅 Created: {backup['timestamp']}")
                        print(Fore.CYAN + f"     📏 Size: {size_mb:.2f} MB")
                        print()

                    total_mb = total_size / (1024 * 1024)
                    print(Fore.WHITE + "=" * 70)
                    print(Fore.GREEN + f"📊 Total backup size: {total_mb:.2f} MB")

            except Exception as e:
                print(Fore.RED + f"\n❌ Error scanning backups: {e}")
                logging.error(f"Backup scan error: {e}")

            input(Fore.YELLOW + "\nPress Enter to continue...")

        elif choice == "0":
            break
        else:
            print(Fore.RED + "\n❌ Invalid choice. Please try again.")
            time.sleep(1)

def run_system_test():
    """Run the system test suite"""
    print(Fore.CYAN + "\n🧪 Running System Test Suite...")
    print(Fore.YELLOW + "This will test the IDM logic system without making changes.")

    confirm = input(Fore.GREEN + "\nProceed with tests? (Y/n): ").lower()
    if confirm == 'n':
        return

    try:
        # Import and run test
        import test_idm_logic
        success = test_idm_logic.run_comprehensive_test()

        if success:
            print(Fore.GREEN + "\n🎉 All tests passed! System is working correctly.")
        else:
            print(Fore.YELLOW + "\n⚠ Some tests failed. Check output above for details.")

    except ImportError:
        print(Fore.RED + "\n❌ Test module not found!")
    except Exception as e:
        print(Fore.RED + f"\n❌ Error running tests: {e}")
        logging.error(f"Test error: {e}")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def check_for_updates():
    """Check if there's a newer version available."""
    print(Fore.CYAN + "\n🔄 Checking for updates...")

    show_progress("Connecting to update server", 2)

    print(Fore.GREEN + "\n✅ You are running the latest version!")
    print(Fore.WHITE + f"Current version: {VERSION}")
    print(Fore.CYAN + "🚀 Enhanced Logic System v2.1")

    input(Fore.YELLOW + "\nPress Enter to return to the main menu...")

def main():
    """Main application function."""
    while True:
        print_header()
        print_menu()

        choice = input(Fore.GREEN + "\nEnter your choice: ")

        if choice == "1":
            activate_idm()
        elif choice == "2":
            freeze_trial()
        elif choice == "3":
            fix_fake_serial_issue()
        elif choice == "4":
            reset_idm()
        elif choice == "5":
            check_idm_status()
        elif choice == "6":
            backup_restore_idm()
        elif choice == "7":
            webbrowser.open("https://www.internetdownloadmanager.com/download.html")
            input(Fore.YELLOW + "\nBrowser opened. Press Enter to continue...")
        elif choice == "8":
            webbrowser.open("https://github.com/zinzied/IDM-Freezer")
            input(Fore.YELLOW + "\nBrowser opened. Press Enter to continue...")
        elif choice == "9":
            run_system_test()
        elif choice == "0":
            print(Fore.GREEN + "\n👋 Thank you for using IDM Freezer & Activation Tool!")
            print(Fore.CYAN + "Enhanced Logic System v2.1 - Fake Serial Fix Edition")
            time.sleep(1)
            sys.exit(0)
        else:
            print(Fore.RED + "\n❌ Invalid choice. Please try again.")
            time.sleep(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(Fore.RED + "\n\n⚠ Program interrupted by user. Exiting...")
        sys.exit(0)
    except Exception as e:
        print(Fore.RED + f"\n\n❌ An unexpected error occurred: {e}")
        logging.error(f"Unexpected error: {e}")
        input(Fore.YELLOW + "\nPress Enter to exit...")
        sys.exit(1)

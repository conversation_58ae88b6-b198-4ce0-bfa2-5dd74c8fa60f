"""
Test script for the fake serial fix functionality
"""

import sys
import os
import logging
from pathlib import Path

# Add current directory to path to import idm_logic
sys.path.insert(0, str(Path(__file__).parent))

try:
    from idm_logic import IDMLogic
    from colorama import Fore, init, just_fix_windows_console
    init(autoreset=True, convert=True, strip=False)
    just_fix_windows_console()
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are installed:")
    print("pip install colorama")
    sys.exit(1)

def test_fake_serial_fix():
    """Test the fake serial fix functionality"""
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.WHITE}Testing Fake Serial Fix Functionality".center(60))
    print(f"{Fore.CYAN}{'='*60}")
    
    idm_logic = IDMLogic()
    
    print(f"\n{Fore.YELLOW}🧪 Testing Enhanced Serial Key Generation...")
    try:
        serial_key = idm_logic._generate_enhanced_serial_key()
        print(f"{Fore.GREEN}✓ Enhanced serial generated: {serial_key}")
        
        # Validate format
        if len(serial_key) == 29 and serial_key.count('-') == 4:
            print(f"{Fore.GREEN}✓ Serial format validation passed")
        else:
            print(f"{Fore.RED}✗ Serial format validation failed")
            
    except Exception as e:
        print(f"{Fore.RED}✗ Enhanced serial generation failed: {e}")
    
    print(f"\n{Fore.YELLOW}🧪 Testing User Info Generation...")
    try:
        user_info = idm_logic.generate_user_info()
        print(f"{Fore.GREEN}✓ User info generated:")
        print(f"  {Fore.CYAN}Name: {user_info['first_name']} {user_info['last_name']}")
        print(f"  {Fore.CYAN}Email: {user_info['email']}")
        
        # Validate email format
        if '@tonec.com' in user_info['email']:
            print(f"{Fore.GREEN}✓ Email format validation passed")
        else:
            print(f"{Fore.RED}✗ Email format validation failed")
            
    except Exception as e:
        print(f"{Fore.RED}✗ User info generation failed: {e}")
    
    print(f"\n{Fore.YELLOW}🧪 Testing Registry Path Detection...")
    try:
        paths = idm_logic.registry_paths
        print(f"{Fore.GREEN}✓ Registry paths detected:")
        print(f"  {Fore.CYAN}CLSID: {paths['clsid_hkcu']}")
        print(f"  {Fore.CYAN}HKLM: {paths['hklm']}")
        print(f"  {Fore.CYAN}DownloadManager: {paths['download_manager']}")
        
    except Exception as e:
        print(f"{Fore.RED}✗ Registry path detection failed: {e}")
    
    print(f"\n{Fore.YELLOW}🧪 Testing CLSID Key Detection...")
    try:
        clsid_keys = idm_logic._find_idm_clsid_keys()
        print(f"{Fore.GREEN}✓ CLSID key detection completed")
        print(f"  {Fore.CYAN}Found {len(clsid_keys)} IDM-related CLSID keys")
        
        if clsid_keys:
            print(f"  {Fore.CYAN}Sample keys:")
            for key in clsid_keys[:3]:  # Show first 3
                print(f"    {Fore.WHITE}{key}")
                
    except Exception as e:
        print(f"{Fore.RED}✗ CLSID key detection failed: {e}")
    
    print(f"\n{Fore.YELLOW}🧪 Testing Backup System...")
    try:
        # Test backup creation (will fail if IDM not installed, but tests the logic)
        backups = idm_logic.get_available_backups()
        print(f"{Fore.GREEN}✓ Backup system functional")
        print(f"  {Fore.CYAN}Found {len(backups)} existing backup(s)")
        
        if backups:
            latest = backups[0]
            print(f"  {Fore.CYAN}Latest: {latest['name']} ({latest['timestamp']})")
            
    except Exception as e:
        print(f"{Fore.RED}✗ Backup system test failed: {e}")
    
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.GREEN}🎉 Fake Serial Fix Test Completed!")
    print(f"{Fore.CYAN}{'='*60}")
    
    print(f"\n{Fore.YELLOW}📋 Summary:")
    print(f"{Fore.WHITE}The fake serial fix includes:")
    print(f"{Fore.CYAN}  ✓ Enhanced serial key generation algorithm")
    print(f"{Fore.CYAN}  ✓ Comprehensive registry cleanup")
    print(f"{Fore.CYAN}  ✓ Advanced CLSID key management")
    print(f"{Fore.CYAN}  ✓ Automatic backup before changes")
    print(f"{Fore.CYAN}  ✓ Validation and testing mechanisms")
    
    print(f"\n{Fore.GREEN}🚀 Ready to fix fake serial issues!")

if __name__ == "__main__":
    try:
        test_fake_serial_fix()
        input(f"\n{Fore.YELLOW}Press Enter to exit...")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Test interrupted by user.")
    except Exception as e:
        print(f"\n{Fore.RED}Unexpected error: {e}")
        input(f"\n{Fore.YELLOW}Press Enter to exit...")
